# Perbaikan Info Pembeli (Owner Info) - Bot Lords Mobile

## 🎯 Masalah yang Diperbaiki

### **Info Pembeli Kosong di Notifikasi BOT_OWNER**

**Masalah:**
```
👤 Info Pembeli:
• Nama: Unknown
• Nomor: unknown  
• ID: <EMAIL>
```

**Penyebab:**
1. Webhook Xendit tidak selalu mengirim metadata lengkap
2. `ownerContactId` menjadi `<EMAIL>` saat metadata kosong
3. Sistem tidak memiliki fallback untuk mendapatkan info pembeli
4. Data pembeli tidak disimpan saat membuat invoice

## 🔧 Solusi yang Diimplementasikan

### **1. Sistem Penyimpanan Data Pembeli**

#### **a. Fungsi `storePaymentData()`**
```javascript
// File: utils/xenditPayment.js
async function storePaymentData(orderId, paymentData) {
    // Simpan data pembeli ke file JSON saat membuat invoice
    const paymentDataFile = path.join(__dirname, '..', 'data', 'payment_data.json');
    
    data[orderId] = {
        groupId: groupId,
        groupName: groupName,
        ownerInfo: ownerInfo, // ✅ Data pembeli disimpan di sini
        duration: duration,
        pricing: pricing,
        invoiceId: invoice.id,
        paymentUrl: invoice.invoiceUrl,
        createdAt: new Date().toISOString()
    };
}
```

#### **b. Integrasi dengan `createPaymentTransaction()`**
```javascript
// Setelah membuat invoice Xendit
const invoice = await xendit.Invoice.createInvoice({ data: requestData });

// ✅ Simpan data pembeli untuk retrieval nanti
await storePaymentData(orderId, {
    groupId: groupId,
    groupName: groupName,
    ownerInfo: ownerInfo, // Data dari command !rent pay
    duration: duration,
    pricing: pricing,
    invoiceId: invoice.id,
    paymentUrl: invoice.invoiceUrl
});
```

### **2. Sistem Multi-Fallback untuk Info Pembeli**

#### **a. Method 1: Dari Stored Payment Data**
```javascript
// File: routes/payment.js
async function getStoredPaymentData(orderId) {
    const paymentDataFile = path.join(__dirname, '..', 'data', 'payment_data.json');
    
    if (fs.existsSync(paymentDataFile)) {
        const data = JSON.parse(fs.readFileSync(paymentDataFile, 'utf8'));
        return data[orderId] || null; // ✅ Ambil data yang disimpan
    }
    return null;
}
```

#### **b. Method 2: Dari Group Participants**
```javascript
async function getOwnerInfoFromGroup(groupId, orderId) {
    const chat = chats.find(c => c.id._serialized === groupId);
    const participants = await chat.getParticipants();
    
    // Cari admin grup (lebih mungkin sebagai pembeli)
    const admins = participants.filter(p => p.isAdmin || p.isSuperAdmin);
    
    for (const admin of admins) {
        const contact = await whatsappClient.getContactById(admin.id._serialized);
        if (contact && contact.pushname && contact.pushname !== 'Unknown') {
            return {
                name: contact.pushname || contact.number,
                number: contact.number,
                id: contact.id._serialized
            };
        }
    }
}
```

#### **c. Method 3: Dari Contact ID (jika valid)**
```javascript
if (whatsappClient && ownerContactId && ownerContactId !== '<EMAIL>') {
    const contact = await whatsappClient.getContactById(ownerContactId);
    ownerInfo = {
        name: contact.pushname || contact.number,
        number: contact.number,
        id: contact.id._serialized
    };
}
```

### **3. Implementasi di `handleSuccessfulPayment()`**

```javascript
// Get owner info with multiple fallback methods
let ownerInfo = null;

// Method 1: Try to get from ownerContactId if valid
if (whatsappClient && ownerContactId && ownerContactId !== '<EMAIL>') {
    try {
        const contact = await whatsappClient.getContactById(ownerContactId);
        ownerInfo = {
            name: contact.pushname || contact.number,
            number: contact.number,
            id: contact.id._serialized
        };
        console.log('✅ Owner info from contact ID:', ownerInfo);
    } catch (error) {
        console.error('Error getting contact info from ID:', error);
    }
}

// Method 2: Try to get from stored payment data
if (!ownerInfo || ownerInfo.name === 'Unknown') {
    try {
        const paymentData = await getStoredPaymentData(orderId);
        if (paymentData && paymentData.ownerInfo) {
            ownerInfo = paymentData.ownerInfo;
            console.log('✅ Owner info from stored payment data:', ownerInfo);
        }
    } catch (error) {
        console.error('Error getting stored payment data:', error);
    }
}

// Method 3: Try to get from group participants
if (!ownerInfo || ownerInfo.name === 'Unknown') {
    try {
        ownerInfo = await getOwnerInfoFromGroup(groupId, orderId);
        if (ownerInfo) {
            console.log('✅ Owner info from group participants:', ownerInfo);
        }
    } catch (error) {
        console.error('Error getting owner info from group:', error);
    }
}

// Method 4: Fallback to unknown
if (!ownerInfo) {
    ownerInfo = {
        name: 'Unknown',
        number: 'unknown',
        id: ownerContactId || '<EMAIL>'
    };
    console.log('⚠️ Using fallback owner info:', ownerInfo);
}
```

## 📊 Hasil Perbaikan

### **Sebelum Perbaikan:**
```
👤 Info Pembeli:
• Nama: Unknown
• Nomor: unknown
• ID: <EMAIL>
```

### **Setelah Perbaikan:**
```
👤 Info Pembeli:
• Nama: Angga Artupas
• Nomor: 6282211219993
• ID: <EMAIL>
```

## 🔄 Alur Kerja Sistem Baru

### **1. Saat User Melakukan `!rent pay X`:**
```
User: !rent pay 7
↓
Bot: Ambil info user dari chatInfo.contact
↓
Bot: Buat invoice Xendit dengan metadata lengkap
↓
Bot: Simpan data pembeli ke payment_data.json ✅
↓
Bot: Kirim link pembayaran ke user
```

### **2. Saat Webhook Diterima:**
```
Webhook: { metadata: null, external_id: "RENT_..." }
↓
Bot: Parse external_id untuk groupId & duration
↓
Bot: Cari info pembeli dengan 4 method:
  1. Contact ID (jika valid)
  2. Stored payment data ✅ (BERHASIL)
  3. Group participants
  4. Fallback unknown
↓
Bot: Gunakan info pembeli yang ditemukan
↓
Bot: Kirim notifikasi dengan info pembeli lengkap ✅
```

## 🧪 Testing

### **Test 1: Stored Payment Data**
```bash
node test-owner-info-fixes.js
```
**Result:** ✅ PASS - Data pembeli tersimpan dan dapat diambil

### **Test 2: Webhook Processing**
```bash
node test-webhook-owner-info.js
```
**Result:** ✅ PASS - Info pembeli berhasil dipopulasi

### **Test 3: Real Webhook**
**Before:** `Nama: Unknown, Nomor: unknown`
**After:** `Nama: Angga Artupas, Nomor: 6282211219993`

## 📁 File yang Dimodifikasi

### **1. `utils/xenditPayment.js`**
- ✅ Tambah fungsi `storePaymentData()`
- ✅ Integrasi dengan `createPaymentTransaction()`
- ✅ Integrasi dengan `createPromoPaymentTransaction()`

### **2. `routes/payment.js`**
- ✅ Tambah fungsi `getStoredPaymentData()`
- ✅ Tambah fungsi `getOwnerInfoFromGroup()`
- ✅ Modifikasi `handleSuccessfulPayment()` dengan multi-fallback

### **3. `data/payment_data.json` (Baru)**
- ✅ File penyimpanan data pembeli
- ✅ Format: `{ "orderId": { ownerInfo, groupInfo, pricing } }`

## 🔒 Keamanan & Performance

### **Data Storage:**
- File JSON disimpan di folder `data/`
- Data dibersihkan otomatis setelah 30 hari (opsional)
- Tidak menyimpan data sensitif (hanya info publik)

### **Fallback Priority:**
1. **Contact ID** (tercepat, jika tersedia)
2. **Stored Data** (sangat reliable)
3. **Group Participants** (backup, butuh WhatsApp client)
4. **Unknown** (fallback terakhir)

### **Error Handling:**
- Setiap method memiliki try-catch
- Log detail untuk debugging
- Graceful degradation ke method berikutnya

## 🎯 Manfaat

### **Untuk BOT_OWNER:**
- ✅ Info pembeli lengkap di notifikasi
- ✅ Tracking siapa yang bayar
- ✅ Data untuk customer service
- ✅ Analytics pembeli

### **Untuk Customer Service:**
- ✅ Identifikasi pembeli dengan mudah
- ✅ Follow-up renewal yang tepat sasaran
- ✅ Personalisasi komunikasi

### **Untuk System Reliability:**
- ✅ Robust fallback system
- ✅ Tidak bergantung pada metadata Xendit
- ✅ Data persistence untuk recovery

## 🚀 Status

**✅ FIXED - Info Pembeli Issue Resolved!**

Sistem sekarang dapat:
- ✅ Menyimpan data pembeli saat membuat invoice
- ✅ Mengambil data pembeli dari multiple sources
- ✅ Menampilkan info pembeli lengkap di notifikasi
- ✅ Fallback gracefully jika satu method gagal
- ✅ Mendukung semua jenis pembayaran (normal & promo)

**Ready for Production! 🎉**
