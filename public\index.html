<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Bot Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <h5 class="sidebar-heading text-white px-3 py-2">WhatsApp Bot</h5>
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#">
                                <i class="bi bi-house-door"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#messages">
                                <i class="bi bi-chat-dots"></i> Messages
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#qr-code">
                                <i class="bi bi-qr-code"></i> QR Code
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#groups">
                                <i class="bi bi-people"></i> Groups
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Dashboard</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="refresh-btn">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                        </div>
                        <a href="/auth/discord" class="btn btn-sm btn-primary">
                            <i class="bi bi-discord"></i> Login with Discord
                        </a>
                    </div>
                </div>

                <!-- QR Code Section -->
                <section id="qr-code" class="mb-4">
                    <h2>WhatsApp QR Code</h2>
                    <p>Scan this QR code with your WhatsApp to authenticate the bot:</p>
                    <div class="qr-container">
                        <div id="qr-placeholder" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Waiting for QR code...</p>
                        </div>
                        <canvas id="qr-code-canvas" class="d-none"></canvas>
                        <div id="qr-expired" class="d-none">
                            <p class="text-danger">QR Code expired. Please refresh the page.</p>
                            <button type="button" class="btn btn-primary" id="refresh-qr-btn">Refresh QR Code</button>
                        </div>
                    </div>
                    <div id="connection-status" class="mt-3">
                        <span class="badge bg-warning">Disconnected</span>
                    </div>
                </section>

                <!-- Groups Section -->
                <section id="groups" class="mb-4">
                    <h2>WhatsApp Groups</h2>
                    <p>Here are all the WhatsApp groups the bot is a member of. You can use these IDs in your .env file.</p>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="alert alert-info mb-0 py-2">
                            <small><i class="bi bi-info-circle"></i> If no groups appear, make sure you're connected to WhatsApp and have joined some groups.</small>
                        </div>
                        <button type="button" class="btn btn-primary" id="refresh-groups-btn">
                            <i class="bi bi-arrow-clockwise"></i> Refresh Groups
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Group ID</th>
                                    <th>Name</th>
                                    <th>Participants</th>
                                    <th>Admin Status</th>
                                    <th>Unread</th>
                                    <th>Last Activity</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="groups-table-body">
                                <!-- Groups will be loaded here -->
                                <tr>
                                    <td colspan="7" class="text-center">Loading groups...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Messages Section -->
                <section id="messages" class="mb-4">
                    <h2>Message Log</h2>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Date/Time</th>
                                    <th>Type</th>
                                    <th>From/To</th>
                                    <th>Message</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="message-table-body">
                                <!-- Messages will be loaded here -->
                                <tr>
                                    <td colspan="6" class="text-center">Loading messages...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Message pagination">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </section>
            </main>
        </div>
    </div>

    <!-- Message Details Modal -->
    <div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="messageModalLabel">Message Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="message-modal-body">
                    <!-- Message details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-danger" id="delete-message-btn">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Group ID Modal -->
    <div class="modal fade" id="groupIdModal" tabindex="-1" aria-labelledby="groupIdModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="groupIdModalLabel">Group ID</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Copy this Group ID to use in your .env file:</p>
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="group-id-input" readonly>
                        <button class="btn btn-outline-secondary" type="button" id="copy-group-id-btn">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                    <div class="alert alert-success d-none" id="copy-success-alert">
                        Group ID copied to clipboard!
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
    <script src="/js/main.js"></script>
</body>
</html>
